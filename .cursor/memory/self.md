# 错误记忆与修正

## 代码错误与修复

### DEXError枚举错误处理

- **错误**: 使用了不存在的 `DEXError::Other`变体
- **修正**: 使用 `DEXError::EyreError(eyre::eyre!("错误信息"))`包装错误信息
- **原因**: 在查看 `DEXError`枚举定义后，发现它没有 `Other`变体，但有 `EyreError`可用于包装通用错误

### 大型数据结构处理

- **错误**: 尝试克隆 `StatusManager`等大型数据结构
- **修正**: 改用引用传递，避免不必要的克隆
- **原因**: 大型数据结构克隆会导致性能问题，特别是在并发环境中

### 池处理并发模式

- **错误**: 使用串行处理大量池数据，效率低下
- **修正**: 拆分为多个职责单一的函数，使用 `FuturesUnordered`并发处理
- **效果**: 显著提高处理速度，更好的资源利用

### 进度显示实现

- **错误**: 进度显示不够详细，特别是在并发环境中
- **修正**: 使用 `AtomicUsize`结合 `eprint!("\r...")`实现实时进度更新
- **效果**: 用户体验更好，进度显示更准确

### 基本原则

- **尽量使用中文回答问题**
- **代码需要简洁可读性高， 更少的函数嵌套，允许部分重复代码，不需要极致的DRY原则**
- **需要多处使用的常量放在const.rs中，只有当前mod使用的常量放在mod中**
- **专注于核心业务逻辑, 简化其他辅助逻辑, 例如: 用于打印进度和统计数量的变量尽量不要用在参数和返回值上，精简相关逻辑。**
- 使用  colored crate 提供彩色日志输出

### calc_values 越界写入与重复逻辑问题

- 错误: `calc_values` 使用 `Vec::with_capacity` 后直接通过索引 `vals[index] = ...` 进行写入，导致越界写入/未初始化内存访问的潜在 panic；并且与 `calc_lowest_value` 内部存在重复的价值计算逻辑。
- 修正: 改为 `push` 累加构建结果，使用前一跳 `r_out` 与上一跳 `value` 进行递推，循环内完成乘以常量 `U_2`；`calc_lowest_value` 改为仅收集 `(r_in, r_out)` 并委托 `calc_values` 完成计算，消除重复逻辑。
- 原因: `with_capacity` 仅预留容量不初始化元素；重复逻辑导致维护成本高且容易产生不一致。
- 建议: 后续类似累积计算函数统一抽象到公共函数，调用方仅做数据收集与错误前置校验，注意不可通过索引写入未初始化的 `Vec`。

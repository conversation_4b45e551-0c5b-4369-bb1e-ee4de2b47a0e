//pub mod bera;
pub mod pls;

use std::{
    collections::{HashMap, HashSet}, default::Default, fs, path::Path, str::FromStr, sync::LazyLock
};
use crate::vira::{dex::{factory::{DexFactory, FACTORY}, DexRouter, DEX}};
use alloy::primitives::{utils::parse_units, Address, U256};
use serde::{Serialize, Deserialize};



#[derive(Default, Debug, Clone, Serialize, Deserialize)]
pub struct Operator {
    pub bull : HashMap<String, String>,
    pub pump : HashMap<String, String>,
}

/*
#[derive(Debug)]
pub enum RouterType {
    V1 = 1, //uniswapV1
    V2, //uniswapV2
    V3 = 3, //uniswapV3
    V2OnlyRouter = 21, //只能通过v2 router swap        //(oec)jf
    V2Stable = 22,      //uniswapV2 stable x3y+y3x //(op)velo
    V2StableFee = 23,  //uniswapV2 stable x3y+y3x and check pair individual fee //(zk)mute
    V2Eoa = 24,   //只能使用eoa账户交易的router
    BeraDex,
}
 */

 #[derive(Default, Debug, Clone, Serialize, Deserialize)]
 pub struct Stable {
    pub addr : Address,
    pub symbol : String,
    pub decimals : u8,

    pub price : f32,

    pub golden_min : U256, //使用黄金分割法求极值的最小值
    pub golden_max : U256, //使用黄金分割法求极值的最大值
 }

 #[derive(Default, Debug, Clone, Serialize, Deserialize)]
pub struct ChainConfig {
    pub name : String,
    pub server : String,

    pub eth : Address,

    pub stables : Vec<Address>,
    pub stable_matrix : Vec<HashSet<Address>>, //stable等价矩阵
    pub contract : Address,

    pub tokens : HashMap<Address, TokenConfig>,
    pub routers: Vec<DEX>,
    pub factories : Vec<FACTORY>,

    pub operators : Operator,

    pub golden : HashMap<Address, (U256, U256)>,
    pub files : FilePath,
}

#[derive(Default, Debug, Clone, Serialize, Deserialize)]
pub struct FilePath {
    pub checkpoint : String,
}

impl ChainConfig {
    pub fn new() -> Self {
        ChainConfig { ..Default::default()}
    }

    pub fn name(mut self, name : &str) -> Self {
        self.name = name.to_string();
        //新建checkpoint文件
        self.files.checkpoint = format!("./data/{}/checkpoint.json.gz", name);
        self
    }

    pub fn contract(mut self, addr : &str) -> Self {
        self.contract = Address::from_str(addr).unwrap();
        self
    }

    pub fn server(mut self, url : &str) -> Self {
        self.server = url.to_string();
        self
    }

    pub fn eth(mut self, addr : &str) -> Self {
        self.eth = Address::from_str(addr).unwrap();
        self
    }

    pub fn stables(mut self, addrs : Vec<&str>) -> Self {
        self.stables = addrs.iter().map(|x| Address::from_str(x).unwrap()).collect();
        self
    }

    pub fn stable_matrix(mut self, addrs : Vec<Vec<&str>>) -> Self {
        self.stable_matrix = addrs.iter().map(|x| x.iter().map(|y| Address::from_str(y).unwrap()).collect()).collect();
        self
    }

    pub fn tokens(mut self, tokens : Vec<TokenConfig>) -> Self {
        tokens.iter().for_each(|x| {
            self.tokens.insert(x.addr, x.clone());
        });
        self
    }

    pub fn routers(mut self, mut routers : Vec<DEX>) -> Self {
        routers.iter_mut().for_each(|x| {
            if  x.config().eth.is_zero() {
                x.config_mut().eth = self.eth;
            }
        });
        self.routers = routers;
        self
    }

    pub fn factories(mut self, factories : Vec<FACTORY>) -> Self {
        self.factories = factories;
        self
    }

    pub fn operators(mut self, operators : Operator) -> Self {
        self.operators = operators;
        self
    }

    pub fn build(mut self) -> Self {
        if self.eth.is_empty() {
            panic!("eth is empty");
        }
        self.routers.iter_mut().for_each(|x| {
            if x.config().eth.is_zero() {
                x.config_mut().eth = self.eth;
            }
        });
        self.factories.iter_mut().for_each(|x| {
            if x.data().eth.is_zero() {
                x.data_mut().eth = self.eth;
            }
        });
        //更新黄金分割法的配置
        self.stables.iter().for_each(|s| {
            let cfg = self.tokens.get(s).unwrap();
            let min_val = 0.0001; //0.0001$
            let max_val = 100000.0; //100000$
            let golden_min = parse_units((min_val / cfg.price).to_string().as_str(), cfg.decimals).unwrap().into();
            let golden_max = parse_units((max_val / cfg.price).to_string().as_str(), cfg.decimals).unwrap().into();
            self.golden.insert(s.clone(), (golden_min, golden_max));
        });

        self
    }


}



#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenConfig {
    pub addr : Address,
    pub name : String,
    pub is_eth : bool,
    pub price : f32,
    pub decimals : u8,
}

impl Default for TokenConfig {
    fn default() -> Self {
        TokenConfig {
            addr : Address::default(),
            name : String::default(),
            is_eth: false,
            price: 1.0f32,
            decimals : 18,
        }
    }
}

impl TokenConfig {
    pub fn new() -> Self {
        TokenConfig {
            ..Default::default()
        }
    }
    pub fn addr(mut self, addr : &str) -> Self {
        self.addr = Address::from_str(addr).unwrap();
        self
    }
    pub fn name(mut self, name : &str) -> Self {
        self.name = name.to_string();
        self
    }
    pub fn is_eth(mut self) -> Self {
        self.is_eth = true;
        self
    }
    pub fn price(mut self, price : f32) -> Self {
        self.price = price;
        self
    }

    pub fn decimals(mut self, d : u8) -> Self {
        self.decimals = d;
        self
    }
}



/*
impl fmt::Debug for DefaultConfig {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("DefaultConfig")
            .field("server", &self.server)
            .field("eth", &self.eth)
            .field("stables", &self.stables)
            .finish()
    }
}
 */

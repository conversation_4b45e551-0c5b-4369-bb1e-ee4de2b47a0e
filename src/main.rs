use std::{error::Error, str::FromStr, sync::{Arc, LazyLock}};

use alloy::primitives::Address;
use config::pls;

use crate::{config::ChainConfig, connector::ChainStatus, vira::Vira};

mod master;
mod config;
mod tools;
mod vira;
mod errors;
mod connector;
mod strategy;
mod test;

// 全局配置单例
pub static CONFIG: LazyLock<ChainConfig> = LazyLock::new(|| {
    crate::config::pls::new()
});
static STATUS: LazyLock<ChainStatus> = LazyLock::new(|| ChainStatus::new());


#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    init().await;
    Ok(())
}

async fn init() {
    let mut vira = vira::Vira::new().await;
    vira.sm.init(vira.connector.clone()).await;

    let amm_stream = vira.sm.subscribe_block(vira.connector.clone()).await.expect("订阅失败");

    let vira = Arc::new(vira);
    let mut strategy = strategy::Strategy::new(vira.clone());

    //let pump_task = strategy.pump.listen(receipt_stream).await;
    let _ = strategy.trash.listen(amm_stream).await;
}


#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_main() {
        init().await;
    }
}
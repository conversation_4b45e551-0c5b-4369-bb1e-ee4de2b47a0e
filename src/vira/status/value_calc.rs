use std::borrow::<PERSON><PERSON>;
use std::sync::Arc;
use alloy::primitives::{Address, U256};
use dashmap::DashMap;

use crate::vira::status::pools::Pools;
use crate::vira::{consts::U_2, pool::DexPool};
use crate::vira::pool::POOL;
use super::{PoolIndex, cache::Cache};
use colored::Colorize;

/// 价值计算器
/// 计算路径和池子的价值
pub struct ValueCalc;

impl ValueCalc {

    /// 获取池子索引的储备量
    pub fn get_reserves_by_index(
        all_pools: &Arc<Pools>,
        flow: &PoolIndex,
        cache: Option<&Cache>
    ) -> (U256, U256) {
        let (r_in, r_out) = if let Some(cache) = cache {
            if let Some(p) = cache.cache_data.get(&flow.addr) {
                p.reserve_by_index(flow.in_index, flow.out_index)
            } else {
                all_pools.data.get(&flow.addr).unwrap().reserve_by_index(flow.in_index, flow.out_index)
            }
        } else {
            all_pools.data.get(&flow.addr).unwrap().reserve_by_index(flow.in_index, flow.out_index)
        };
        (r_in, r_out)
    }

    /// 计算最低价值 -> (每一跳的价值序列, 路径中的最低价值)
    /// 当前逻辑会在计算最低值时排除 `cache.exclude_pool` 对应的主池（若提供 cache）。
    /// 优化要点：
    /// - 内部核心价值序列的计算统一委托给 `calc_values`，避免重复逻辑
    /// - 仍保留对 r_out==0 的防御性检查与调试输出，保持原有错误处理语义
    /// - 保持对主池的排除规则与边界条件（单跳路径）一致
    pub fn calc_lowest_value<T>(
        pools_flow: &[T],
        all_pools: &Arc<Pools>,
        cache: Option<&Cache>
    ) -> (Vec<U256>, U256,)
    where
        T: Borrow<PoolIndex> + std::fmt::Debug,
    {
        // 1) 读取每一跳对应池子的储备并进行基本合法性校验（与原实现保持一致）
        //    - 保留 r_out==0 的快速失败（panic），避免后续除零
        //    - 收集成 (r_in, r_out) 列表，供统一计算使用
        let mut reserves: Vec<(U256, U256)> = Vec::with_capacity(pools_flow.len());
        for flow in pools_flow.iter() {
            let flow: &PoolIndex = flow.borrow();
            let (r_in, r_out) = Self::get_reserves_by_index(all_pools, flow, cache);
            if r_out.is_zero() {
                eprintln!("pools_flow: {:?}", flow);
                eprintln!("r_in: {:?}, r_out: {:?}", r_in, r_out);
                panic!("r_out is zero");
            }
            reserves.push((r_in, r_out));
        }

        // 2) 核心价值计算委托给 calc_values，得到每跳的价值（已 x2）
        let values: Vec<U256> = Self::calc_values(reserves);

        // 3) 计算最低价值：从 values 集合中找到最小值
        let mut lowest_value = U256::MAX;
        let mut min_value_index = 0;

        // 找到最小值及其索引
        for (i, &value) in values.iter().enumerate() {
            if value < lowest_value {
                lowest_value = value;
                min_value_index = i;
            }
        }

        // 4) 判断最小值的索引是否对应 cache.exclude_pool 所在的池子
        let is_min_value_at_exclude_pool = if let Some(cache) = cache {
            if min_value_index < pools_flow.len() {
                let flow: &PoolIndex = pools_flow[min_value_index].borrow();
                flow.addr == cache.exclude_pool
            } else {
                false
            }
        } else {
            false
        };

        (values, lowest_value, is_min_value_at_exclude_pool)
    }


    /// 按路径顺序计算每一跳的价值，并将结果乘以 2（与原实现保持一致）
    /// 输入为各跳的储备对 (r_in, r_out)
    /// 计算规则：
    /// - 第 0 跳：value = r_in
    /// - 第 i 跳 (i>0)：若上一步的 r_out==0，则该跳 value=0；否则 value = 上一步 value * r_in / 上一步 r_out
    /// - 最终返回的每个 value 都会乘以常量 U_2
    pub fn calc_values(reserves: Vec<(U256, U256)>) -> Vec<U256> {
        let mut values_doubled: Vec<U256> = Vec::with_capacity(reserves.len());
        if reserves.is_empty() {
            return values_doubled;
        }

        let mut pre_val: U256 = U256::ZERO;
        let mut pre_out_r: U256 = U256::ZERO;

        for (index, (r_in, r_out)) in reserves.into_iter().enumerate() {
            let val = if index == 0 {
                r_in
            } else if pre_out_r.is_zero() {
                // 与旧逻辑保持一致：若上一跳的 r_out 为 0，当前价值记为 0，而非除零
                U256::ZERO
            } else {
                pre_val * r_in / pre_out_r
            };

            values_doubled.push(val * U_2);

            pre_val = val;
            pre_out_r = r_out;
        }

        values_doubled
    }

    /// 计算池子的价值
    pub fn calc_value_of_pool(pre : Option<PoolIndex>, pool : POOL, pools : Arc<Pools>) -> (Address, U256) {



        (Address::ZERO, U256::ZERO)
    }

}
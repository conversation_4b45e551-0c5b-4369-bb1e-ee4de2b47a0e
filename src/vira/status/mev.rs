use alloy::primitives::{Address, U256};
use serde::{Deserialize, Serialize};
use std::sync::Arc;

use crate::tools;
use crate::vira::pool::{DexPool, POOL};
use crate::vira::consts::{MAX_GOLDEN_STEPS, U_1000, U_10000, U_618, UINT112_MAX, U_2, U_MAX_U112};
use crate::vira::status::pools::Pools;

use super::cache::CacheArbitrage;

#[derive(Debug, <PERSON>lone, Default, Serialize, Deserialize)]
pub struct MevPool {
    pub addr : Address,
    pub in_index : usize,
    pub out_index : usize,

    pub fee : U256,       //正向交易fee，fee of out_token transfer out from pool 
    pub fee_desc : U256, //反向交易fee, fee of in_token transfer out from pool
}

#[derive(Debug, <PERSON><PERSON>, Default, Serialize, Deserialize)]
pub struct Mev {
    pub s_in : Address,
    pub s_out : Address,
    pub convert_eth : bool,
    pub weight : f32, //除去主pool权重最低的pool

    pub pools : Vec<MevPool>,

    // 0: v1和v2, 使用dydx求最大值, 
    // 1:  
    // 2: 固定amountIn, 但是有preview
    // 3: no preview, 固定amountIn
    pub calc : u8,

    //下面的属性需要在线获取
    pub status : MevStatus, 
    pub status_desc : MevStatus,
    pub gas : U256,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize, PartialEq)]
pub enum MevStatus {
    #[default] Unchecked,
    Active,
    Bad,
}

#[derive(PartialEq, Clone, Copy, Debug)]
pub enum Order {
    Asc,
    Desc,
}

//黄金分割法求极值
pub fn find_max_golden(
    amount_min: U256,
    amount_max: U256,
    mev: &Mev,
    pool_refs: &[POOL],
    order: Order
) -> U256 {
    let mut step = 0u8;

    // 提前检查最小值是否有利润
    let mut amount_out1 = get_amount_out_by_pool(amount_min, mev, &pool_refs, &order);
    if amount_out1.lt(&amount_min) { 
        return U256::ZERO;
    }

    // 4. 初始化搜索区间
    let mut low = amount_min;
    let mut high = amount_max;

    // 使用黄金分割比例 1.618034
    let mut x1 = high - ((high - low) * U_618 / U_1000);
    let mut x2 = low + ((high - low) * U_618 / U_1000);

    // 5. 预先计算初始点的输出值
    let mut amount_out2 = get_amount_out_by_pool(x2, mev, &pool_refs, &order);
    
    // 使用 max 来避免溢出
    let mut f1 = amount_out1 + UINT112_MAX - x1;
    let mut f2 = amount_out2 + UINT112_MAX - x2;

    // 6. 优化主循环
    while high > low + amount_min && step < MAX_GOLDEN_STEPS {
        if f1 > f2 {
            high = x2;
            x2 = x1;
            f2 = f1;
            x1 = high - ((high - low) * U_618 / U_1000);
            amount_out1 = get_amount_out_by_pool(x1, mev, &pool_refs, &order);
            f1 = amount_out1 + UINT112_MAX - x1;
        } else if f1 < f2 {
            low = x1;
            x1 = x2;
            f1 = f2;
            x2 = low + ((high - low) * U_618 / U_1000);
            amount_out2 = get_amount_out_by_pool(x2, mev, &pool_refs, &order);
            f2 = amount_out2 + UINT112_MAX - x2;
        } else {
            break;
            /*
            low = x1;
            high = x2;
            x1 = high - ((high - low) * U256_618 / U256_1000);
            x2 = low + ((high - low) * U256_618 / U256_1000);
            amount_out1 = get_amount_out_by_path_with_refs(x1, mev, &refs, &order);
            amount_out2 = get_amount_out_by_path_with_refs(x2, mev, &refs, &order);
            f1 = amount_out1 + UINT112_MAX - x1;
            f2 = amount_out2 + UINT112_MAX - x2;
            */
        }
        step += 1;
    }

    // 7. 返回结果
    //dbg!((high + low) / U256_2, step);
    (high + low) / U_2
}

/// 计算uni_v2 POOLS的最大利润
/// 此函数将通过多个 Uniswap V2 类型池的交易序列建模等效储备，计算最优套利金额。
/// # 返回
/// * `Option<U256>` - 如果找到可盈利的套利机会且金额在有效范围内，则返回最优输入金额。
///   否则返回 `None`。
pub fn find_optimal_amount_v2(pools: Arc<Pools>, cache: &CacheArbitrage, mev: &Mev, order: &Order) -> Option<U256> {
    // 验证输入参数
    if mev.pools.is_empty() {
        return None;
    }

    // 辅助函数，用于安全地检索池数据（储备金和有效费用）
    // 优先从cache中获取，如果没有则从pools获取
    let get_pool_data = |pool_index: &MevPool, fee: &U256, order0: &Order| -> Option<(U256, U256, U256)> {
        // 根据交易方向确定输入输出索引
        let (in_index, out_index) = match order0 {
            Order::Asc => (pool_index.in_index, pool_index.out_index),
            Order::Desc => (pool_index.out_index, pool_index.in_index),
        };

        // 验证索引有效性的辅助函数
        let validate_and_get_reserves = |data: &crate::vira::pool::PoolData| -> Option<(U256, U256, U256)> {
            // 检查索引是否有效
            if in_index >= data.tokens.len() || out_index >= data.tokens.len() {
                return None; // 改为返回None而不是panic
            }

            let reserve_in = data.tokens[in_index].reserve;
            let reserve_out = data.tokens[out_index].reserve;

            // 检查储备金是否为零
            if reserve_in.is_zero() || reserve_out.is_zero() {
                return None;
            }

            // 计算有效费用 = 10000 - 交易费 - 协议费
            // 确保不会下溢
            let total_fee = fee.saturating_add(data.fp);
            if total_fee >= U_10000 {
                return None; // 费用过高，无效
            }
            let effective_fee = U_10000 - total_fee;

            Some((reserve_in, reserve_out, effective_fee))
        };

        // 首先尝试从缓存中获取
        if let Some(cached_pool) = cache.data.get(&pool_index.addr) {
            let data = cached_pool.data();
            if let Some(result) = validate_and_get_reserves(&data) {
                return Some(result);
            }
        }

        // 如果缓存中没有或数据无效，从pools获取
        pools.data.get(&pool_index.addr).and_then(|p| {
            let data = p.data();
            validate_and_get_reserves(&data)
        })
    };

    // 根据交易方向确定池子和费用的处理顺序
    let pools_to_process: Vec<&MevPool> = match order {
        Order::Asc => mev.pools.iter().collect(),
        Order::Desc => mev.pools.iter().rev().collect(),
    };

    // 使用第一个池的储备金和有效费用初始化等效储备 (Ea, Eb)
    // (Ea, Eb) 代表一个虚拟池的储备金，该虚拟池模拟了整个交易链
    let first_mev_pool = pools_to_process[0];
    let first_fee = if *order == Order::Asc {first_mev_pool.fee_desc } else { first_mev_pool.fee };
    let (mut ea, mut eb, efee) = match get_pool_data(first_mev_pool, &first_fee, order) {
        Some(data) => data,
        None => return None,
    };

    // 迭代处理链中剩余的池，以更新等效储备
    // 注意：与Solidity合约保持一致，使用当前池的费用进行计算
    for i in 1..pools_to_process.len() {
        let mev_pool = pools_to_process[i];
        let fee_val = if *order == Order::Asc {mev_pool.fee_desc } else { mev_pool.fee };;

        // 获取序列中当前池的储备金 (reserve_in, reserve_out) 和费用
        let (reserve_in, reserve_out, current_fee) = match get_pool_data(mev_pool, &fee_val, order) {
            Some(data) => data,
            None => return None,
        };

        // 核心算法：与Solidity合约完全一致的等效储备更新逻辑
        // 分母计算：reserve_in + (Eb * current_fee / 10000)
        let eb_fee_term = match eb.checked_mul(current_fee) {
            Some(val) => match val.checked_div(U_10000) {
                Some(result) => result,
                None => return None,
            },
            None => return None,
        };

        let denominator = match reserve_in.checked_add(eb_fee_term) {
            Some(val) => val,
            None => return None,
        };

        if denominator.is_zero() {
            return None;
        }

        // 更新 Ea: Ea_new = Ea_old * reserve_in / denominator
        ea = match ea.checked_mul(reserve_in) {
            Some(val) => match val.checked_div(denominator) {
                Some(result) => result,
                None => return None,
            },
            None => return None,
        };

        // 更新 Eb: Eb_new = Eb_old * reserve_out * current_fee / 10000 / denominator
        // 按照Solidity合约的顺序进行计算
        let eb_temp = match eb.checked_mul(reserve_out) {
            Some(val) => val,
            None => return None,
        };

        let eb_temp2 = match eb_temp.checked_mul(current_fee) {
            Some(val) => val,
            None => return None,
        };

        let eb_temp3 = match eb_temp2.checked_div(U_10000) {
            Some(val) => val,
            None => return None,
        };

        eb = match eb_temp3.checked_div(denominator) {
            Some(val) => val,
            None => return None,
        };
    }

    // 处理完所有池后，(Ea, Eb) 是整个链的最终等效储备
    // 现在，为这个单一的等效池计算最优输入金额
    // 注意：使用第一个池的费用(efee)进行最终计算，与Solidity合约一致
    if ea.is_zero() || eb.is_zero() || efee.is_zero() {
        return None;
    }

    // 最优输入金额的公式：optimal_amount = (sqrt(Ea * Eb * Efee / 10000) - Ea) * 10000 / Efee
    // 计算 sqrt 部分，防止溢出
    let product = match ea.checked_mul(eb) {
        Some(val) => val,
        None => return None,
    };

    let sqrt_part = match product.checked_mul(efee) {
        Some(val) => match val.checked_div(U_10000) {
            Some(result) => result,
            None => return None,
        },
        None => return None,
    };

    let sqrt_value = tools::sqrt(sqrt_part);

    // 如果 sqrt_value <= Ea，意味着没有套利机会（利润将为负）
    if sqrt_value <= ea {
        return None;
    }

    // 计算最优金额，防止溢出和除零
    let numerator = match sqrt_value.saturating_sub(ea).checked_mul(U_10000) {
        Some(val) => val,
        None => return None,
    };

    let optimal_amount = match numerator.checked_div(efee) {
        Some(val) => val,
        None => return None,
    };

    // 检查结果是否在合理范围内
    // 使用 uint112 的最大值作为上限
    if optimal_amount.is_zero() || optimal_amount >= U_MAX_U112 {
        None
    } else {
        Some(optimal_amount)
    }
}




/// 计算通过池子路径交换后的最终输出金额
///
/// 简化版本：直接在函数内处理所有逻辑，减少函数调用层次
#[inline(always)]
pub fn get_amount_out_by_pool(
    initial_input_amount: U256,
    mev_path: &Mev,
    pool_references: &[POOL],
    swap_direction: &Order
) -> U256 {
    let mut current_amount = initial_input_amount;
    let pool_count = mev_path.pools.len();

    // 直接遍历池子，根据方向计算索引
    for step in 0..pool_count {
        let pool_index = match swap_direction {
            Order::Asc => step,
            Order::Desc => pool_count - 1 - step,
        };

        let pool_config = &mev_path.pools[pool_index];
        let pool_ref = &pool_references[pool_index];
        let pool_data = pool_ref.data();

        // 根据交换方向获取参数
        let (fee, token_in, token_out) = match swap_direction {
            Order::Asc => (
                pool_config.fee_desc,
                pool_data.tokens[pool_config.in_index].addr,
                pool_data.tokens[pool_config.out_index].addr,
            ),
            Order::Desc => (
                pool_config.fee,
                pool_data.tokens[pool_config.out_index].addr,
                pool_data.tokens[pool_config.in_index].addr,
            ),
        };

        // 执行交换模拟
        match pool_ref.simulate_swap(token_in, token_out, current_amount, Some(fee)) {
            Ok(amount_out) => current_amount = amount_out,
            Err(_) => return U256::ZERO,
        }
    }

    current_amount
}


/// 零克隆版本：计算通过池子路径交换的所有中间输出金额
///
/// 优化特点：
/// 1. 接受池子引用迭代器，避免创建临时向量
/// 2. 使用泛型支持不同的引用类型（HashMap::get 和 DashMap::get）
/// 3. 完全消除克隆操作，提升性能
/// 4. 保持与原函数相同的返回格式和语义
#[inline(always)]
pub fn get_amount_outs_by_pool<'a, I>(
    initial_input_amount: U256,
    mev_path: &Mev,
    pool_refs_iter: I,
    swap_direction: &Order
) -> Vec<U256>
where
    I: Iterator<Item = &'a POOL> + ExactSizeIterator,
{
    let mut amounts = Vec::with_capacity(mev_path.pools.len() + 1);
    amounts.push(initial_input_amount); // 第一个元素是输入金额

    let mut current_amount = initial_input_amount;
    let pool_count = mev_path.pools.len();

    // 根据交换方向确定处理顺序
    let pool_indices: Vec<usize> = match swap_direction {
        Order::Asc => (0..pool_count).collect(),
        Order::Desc => (0..pool_count).rev().collect(),
    };

    // 将池子引用收集到向量中以支持索引访问
    let pool_refs: Vec<&POOL> = pool_refs_iter.collect();

    // 遍历每个池子进行交换模拟
    for &pool_index in &pool_indices {
        let pool_info = &mev_path.pools[pool_index];
        let pool_ref = pool_refs[pool_index];
        let pool_data = pool_ref.data();

        // 根据交换方向确定费用、输入和输出token
        let (custom_fee, token_in, token_out) = match swap_direction {
            Order::Asc => (
                pool_info.fee_desc,
                pool_data.tokens[pool_info.in_index].addr,
                pool_data.tokens[pool_info.out_index].addr,
            ),
            Order::Desc => (
                pool_info.fee,
                pool_data.tokens[pool_info.out_index].addr,
                pool_data.tokens[pool_info.in_index].addr,
            ),
        };

        // 执行交换模拟
        match pool_ref.simulate_swap(token_in, token_out, current_amount, Some(custom_fee)) {
            Ok(amount_out) => {
                current_amount = amount_out;
                amounts.push(amount_out);
            },
            Err(_) => return vec![initial_input_amount], // 交换失败，返回初始金额
        }
    }

    amounts
}



/// 零克隆池子交换模拟：统一处理不同来源的池子引用
///
/// 这是从 trash.rs 重构而来的通用函数，提供统一的池子交换模拟
///
/// 优化特点：
/// 1. 统一处理缓存和主存储的池子引用
/// 2. 内联优化，减少函数调用开销
/// 3. 直接使用池子引用进行交换模拟
/// 4. 早期退出，交换失败时立即返回None
///
/// # 参数
/// * `pool` - 池子引用
/// * `pool_info` - 池子信息（包含输入输出索引）
/// * `mev_path` - MEV路径信息
/// * `pool_index` - 池子在路径中的索引
/// * `amount_in` - 输入金额
/// * `swap_direction` - 交换方向
///
/// # 返回值
/// * `Option<U256>` - 交换输出金额，如果交换失败则返回None
#[inline(always)]
pub fn simulate_swap_with_pool_ref(
    pool: &POOL,
    pool_info: &MevPool,
    mev_path: &Mev,
    pool_index: usize,
    amount_in: U256,
    swap_direction: &Order
) -> Option<U256> {
    let pool_data = pool.data();

    // 根据交换方向确定费用、输入和输出token
    let (custom_fee, token_in, token_out) = match swap_direction {
        Order::Asc => (
            pool_info.fee_desc,
            pool_data.tokens[pool_info.in_index].addr,
            pool_data.tokens[pool_info.out_index].addr,
        ),
        Order::Desc => (
            pool_info.fee,
            pool_data.tokens[pool_info.out_index].addr,
            pool_data.tokens[pool_info.in_index].addr,
        ),
    };

    // 执行交换模拟
    match pool.simulate_swap(token_in, token_out, amount_in, Some(custom_fee)) {
        Ok(amount_out) => Some(amount_out),
        Err(_) => None, // 交换失败，返回None
    }
}


#[cfg(test)]
mod tests {
    // 测试代码将在后续添加
}
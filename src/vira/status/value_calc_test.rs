#[cfg(test)]
mod tests {
    use super::*;
    use alloy::primitives::{Address, U256};
    use std::sync::Arc;
    use crate::vira::{
        status::{pools::Pools, cache::Cache, PoolIndex},
        pool::{DexPool, PoolData, PoolDataToken, Status, SwapWay, POOL},
        dex::uni_v2::pool::UniV2Pool,
    };
    use dashmap::DashMap;

    // 创建测试用的池子
    fn create_test_pool(addr: Address, reserve0: U256, reserve1: U256) -> POOL {
        let token0 = PoolDataToken {
            addr: Address::from([1u8; 20]),
            reserve: reserve0,
            decimals: 18,
            symbol: "TOKEN0".to_string(),
        };
        let token1 = PoolDataToken {
            addr: Address::from([2u8; 20]),
            reserve: reserve1,
            decimals: 18,
            symbol: "TOKEN1".to_string(),
        };

        let pool_data = PoolData {
            addr,
            tokens: vec![token0, token1],
            fee: U256::from(3000),
            ver: 2,
            swap_way: SwapWay::Pool,
            status: Status::Active,
        };

        POOL::UniV2(UniV2Pool::new(pool_data))
    }

    #[test]
    fn test_calc_lowest_value_exclude_main_pool() {
        // 创建测试池子
        let main_pool_addr = Address::from([10u8; 20]);
        let other_pool_addr = Address::from([20u8; 20]);
        
        let main_pool = create_test_pool(main_pool_addr, U256::from(1000), U256::from(2000));
        let other_pool = create_test_pool(other_pool_addr, U256::from(500), U256::from(1000));

        // 创建 Pools 结构
        let pools = Arc::new(Pools {
            data: DashMap::new(),
            mevs: DashMap::new(),
        });
        pools.data.insert(main_pool_addr, main_pool);
        pools.data.insert(other_pool_addr, other_pool);

        // 创建池子流向
        let pools_flow = vec![
            PoolIndex {
                addr: main_pool_addr,
                in_index: 0,
                out_index: 1,
                in_token: Address::from([1u8; 20]),
                out_token: Address::from([2u8; 20]),
            },
            PoolIndex {
                addr: other_pool_addr,
                in_index: 0,
                out_index: 1,
                in_token: Address::from([1u8; 20]),
                out_token: Address::from([2u8; 20]),
            },
        ];

        // 创建缓存，设置主池为排除池
        let cache = Cache::new(&main_pool_addr);

        // 测试 is_exclude_main_pool = false（原逻辑）
        let (values_false, lowest_false, is_min_at_exclude_false) = 
            ValueCalc::calc_lowest_value(&pools_flow, &pools, Some(&cache), false);

        // 测试 is_exclude_main_pool = true（新逻辑）
        let (values_true, lowest_true, is_min_at_exclude_true) = 
            ValueCalc::calc_lowest_value(&pools_flow, &pools, Some(&cache), true);

        // 验证结果
        assert_eq!(values_false.len(), 2);
        assert_eq!(values_true.len(), 2);
        
        // 当 is_exclude_main_pool = true 时，is_min_value_at_exclude_pool 应该强制为 false
        assert_eq!(is_min_at_exclude_true, false);
        
        // 值应该相同（因为计算逻辑相同）
        assert_eq!(values_false, values_true);
        
        // 但最小值可能不同，因为排除了主池
        println!("is_exclude_main_pool = false: lowest = {}, is_min_at_exclude = {}", 
                 lowest_false, is_min_at_exclude_false);
        println!("is_exclude_main_pool = true: lowest = {}, is_min_at_exclude = {}", 
                 lowest_true, is_min_at_exclude_true);
    }

    #[test]
    fn test_calc_lowest_value_no_cache() {
        // 创建测试池子
        let pool_addr = Address::from([10u8; 20]);
        let pool = create_test_pool(pool_addr, U256::from(1000), U256::from(2000));

        let pools = Arc::new(Pools {
            data: DashMap::new(),
            mevs: DashMap::new(),
        });
        pools.data.insert(pool_addr, pool);

        let pools_flow = vec![
            PoolIndex {
                addr: pool_addr,
                in_index: 0,
                out_index: 1,
                in_token: Address::from([1u8; 20]),
                out_token: Address::from([2u8; 20]),
            },
        ];

        // 测试无缓存情况
        let (values_false, lowest_false, is_min_at_exclude_false) = 
            ValueCalc::calc_lowest_value(&pools_flow, &pools, None, false);

        let (values_true, lowest_true, is_min_at_exclude_true) = 
            ValueCalc::calc_lowest_value(&pools_flow, &pools, None, true);

        // 无缓存时，两种模式应该产生相同结果
        assert_eq!(values_false, values_true);
        assert_eq!(lowest_false, lowest_true);
        assert_eq!(is_min_at_exclude_false, false);
        assert_eq!(is_min_at_exclude_true, false);
    }
}
